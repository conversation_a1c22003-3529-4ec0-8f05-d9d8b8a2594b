{{- if and .Values.autoscaling.keda.enabled .Values.autoscaling.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ include "ocr-wrapper.fullname" . }}-keda
  labels:
    {{- include "ocr-wrapper.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    name: {{ include "ocr-wrapper.fullname" . }}
  minReplicaCount: {{ .Values.autoscaling.minReplicas }}
  maxReplicaCount: {{ .Values.autoscaling.maxReplicas }}
  triggers:
    {{- range .Values.autoscaling.keda.triggers }}
    - type: {{ .type }}
      metadata:
        {{- toYaml .metadata | nindent 8 }}
    {{- end }}
{{- end }}
