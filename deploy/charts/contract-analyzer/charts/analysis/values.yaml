# Default values for analysis service
replicaCount: 2

image:
  repository: contract-analyzer/analysis
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 3000
  targetPort: 3000

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: analysis.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
  # KEDA scaling based on RabbitMQ queue length
  keda:
    enabled: true
    triggers:
      - type: rabbitmq
        metadata:
          queueName: analysis-queue
          host: amqp://guest:guest@rabbitmq:5672
          queueLength: "10"

nodeSelector: {}

tolerations: []

affinity: {}

# Environment variables
env:
  NODE_ENV: development
  PORT: "3000"

# ConfigMap data
config: {}

# Secret data (base64 encoded)
secrets: {}

# Health check configuration
healthCheck:
  enabled: true
  path: /healthz
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
  successThreshold: 1

# Readiness probe configuration
readinessProbe:
  enabled: true
  path: /healthz
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
  successThreshold: 1
