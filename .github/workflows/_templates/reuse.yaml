name: Reusable Workflow Steps

on:
  workflow_call:
    inputs:
      service:
        description: 'Service name to build'
        required: true
        type: string
      node-version:
        description: 'Node.js version'
        required: false
        type: string
        default: '20'
      coverage-threshold:
        description: 'Test coverage threshold'
        required: false
        type: number
        default: 70
    outputs:
      image-tag:
        description: 'Docker image tag'
        value: ${{ jobs.build.outputs.image-tag }}

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/${{ inputs.service }}

jobs:
  setup:
    name: Setup Dependencies
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'npm'
          cache-dependency-path: 'contract-analyzer/package-lock.json'

      - name: Install dependencies
        run: |
          cd contract-analyzer
          npm ci

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: contract-analyzer/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('contract-analyzer/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-node-

  lint:
    name: Lint
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'npm'
          cache-dependency-path: 'contract-analyzer/package-lock.json'

      - name: Install dependencies
        run: |
          cd contract-analyzer
          npm ci

      - name: Run lint
        run: |
          cd contract-analyzer
          npx nx lint ${{ inputs.service }}

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'npm'
          cache-dependency-path: 'contract-analyzer/package-lock.json'

      - name: Install dependencies
        run: |
          cd contract-analyzer
          npm ci

      - name: Run tests with coverage
        run: |
          cd contract-analyzer
          npx nx test ${{ inputs.service }} --coverage --coverageThreshold='{"global":{"branches":${{ inputs.coverage-threshold }},"functions":${{ inputs.coverage-threshold }},"lines":${{ inputs.coverage-threshold }},"statements":${{ inputs.coverage-threshold }}}}'

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          file: contract-analyzer/coverage/lcov.info
          flags: ${{ inputs.service }}
          name: ${{ inputs.service }}-coverage
          fail_ci_if_error: false

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'npm'
          cache-dependency-path: 'contract-analyzer/package-lock.json'

      - name: Install dependencies
        run: |
          cd contract-analyzer
          npm ci

      - name: Run Snyk to check for Node.js vulnerabilities
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=contract-analyzer/package.json

  build:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [lint, test, security]
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=${{ github.sha }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: contract-analyzer/apps/${{ inputs.service }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            SERVICE=${{ inputs.service }}

      - name: Run Snyk to check Docker image for vulnerabilities
        uses: snyk/actions/docker@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          args: --severity-threshold=high
