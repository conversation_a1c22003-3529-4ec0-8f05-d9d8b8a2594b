{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022"], "module": "nodenext", "moduleResolution": "nodenext", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "customConditions": ["development"]}}