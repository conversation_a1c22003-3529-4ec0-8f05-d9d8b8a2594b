{"name": "@contract-analyzer/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "devDependencies": {"@eslint/js": "^9.8.0", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/eslint": "21.1.3", "@nx/eslint-plugin": "21.1.3", "@nx/jest": "21.1.3", "@nx/js": "21.1.3", "@nx/nest": "^21.1.3", "@nx/node": "21.1.3", "@nx/web": "21.1.3", "@nx/webpack": "21.1.3", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@types/jest": "^29.5.12", "@types/node": "~18.16.9", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "nx": "21.1.3", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "webpack-cli": "^5.1.4"}, "workspaces": ["packages/*", "apps", "apps-e2e", "apps/*", "libs/*"], "dependencies": {"@nestjs/common": "^10.0.2", "@nestjs/core": "^10.0.2", "@nestjs/platform-express": "^10.0.2", "axios": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.0"}}