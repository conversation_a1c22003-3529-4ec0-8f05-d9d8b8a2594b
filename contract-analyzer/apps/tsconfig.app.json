{"extends": "../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node"], "rootDir": "src", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "experimentalDecorators": true, "emitDecoratorMetadata": true, "target": "es2021"}, "include": ["src/**/*.ts"], "exclude": ["out-tsc", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}