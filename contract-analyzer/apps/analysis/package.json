{"name": "@contract-analyzer/analysis", "version": "0.0.1", "private": true, "nx": {"name": "analysis", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "analysis:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "analysis:build:development"}, "production": {"buildTarget": "analysis:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}}