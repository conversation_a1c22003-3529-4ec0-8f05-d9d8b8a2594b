{"name": "@contract-analyzer/auth", "version": "0.0.1", "private": true, "nx": {"name": "auth", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "auth:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "auth:build:development"}, "production": {"buildTarget": "auth:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}}