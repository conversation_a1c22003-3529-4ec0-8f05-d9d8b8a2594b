{"name": "@contract-analyzer/citation", "version": "0.0.1", "private": true, "nx": {"name": "citation", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "citation:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "citation:build:development"}, "production": {"buildTarget": "citation:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}}