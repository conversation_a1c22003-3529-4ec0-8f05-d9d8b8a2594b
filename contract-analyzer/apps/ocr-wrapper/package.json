{"name": "@contract-analyzer/ocr-wrapper", "version": "0.0.1", "private": true, "nx": {"name": "ocr-wrapper", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ocr-wrapper:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ocr-wrapper:build:development"}, "production": {"buildTarget": "ocr-wrapper:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}}