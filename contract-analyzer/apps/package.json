{"name": "@contract-analyzer/api-gateway", "version": "0.0.1", "private": true, "nx": {"name": "api-gateway", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "api-gateway:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "api-gateway:build:development"}, "production": {"buildTarget": "api-gateway:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}}