{"name": "@contract-analyzer/ocr-wrapper-e2e", "version": "0.0.1", "private": true, "nx": {"name": "ocr-wrapper-e2e", "implicitDependencies": ["ocr-wrapper"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{projectRoot}/test-output/jest/coverage"], "options": {"jestConfig": "apps/ocr-wrapper-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["ocr-wrapper:build", "ocr-wrapper:serve"]}}}}