version: '3.8'

services:
  # Application Services
  api-gateway:
    build:
      context: .
      dockerfile: contract-analyzer/apps/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/contract_analyzer
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QDRANT_URL=http://qdrant:6333
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - contract-analyzer-network

  auth:
    build:
      context: .
      dockerfile: contract-analyzer/apps/auth/Dockerfile
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/contract_analyzer
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - contract-analyzer-network

  analysis:
    build:
      context: .
      dockerfile: contract-analyzer/apps/analysis/Dockerfile
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/contract_analyzer
      - QDRANT_URL=http://qdrant:6333
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
    depends_on:
      - postgres
      - qdrant
      - rabbitmq
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - contract-analyzer-network

  citation:
    build:
      context: .
      dockerfile: contract-analyzer/apps/citation/Dockerfile
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/contract_analyzer
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
    depends_on:
      - postgres
      - rabbitmq
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - contract-analyzer-network

  ocr-wrapper:
    build:
      context: .
      dockerfile: contract-analyzer/apps/ocr-wrapper/Dockerfile
    ports:
      - "3004:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
    depends_on:
      - rabbitmq
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - contract-analyzer-network

  # Infrastructure Services
  postgres:
    image: postgres:16-alpine
    environment:
      - POSTGRES_DB=contract_analyzer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - contract-analyzer-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - contract-analyzer-network

  qdrant:
    image: qdrant/qdrant:v1.8.0
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - contract-analyzer-network

  rabbitmq:
    image: rabbitmq:3.13-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - contract-analyzer-network

  # Observability Services
  jaeger:
    image: jaegertracing/all-in-one:1.52
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - contract-analyzer-network

  prometheus:
    image: prom/prometheus:v2.48.0
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - contract-analyzer-network

  grafana:
    image: grafana/grafana:10.2.0
    ports:
      - "3005:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - contract-analyzer-network

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:

networks:
  contract-analyzer-network:
    driver: bridge
